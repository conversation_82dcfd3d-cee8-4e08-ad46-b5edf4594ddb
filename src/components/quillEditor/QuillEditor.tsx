import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import { App as AntdApp } from 'antd';
import { Maximize, Minimize } from 'lucide-react';
import styles from './QuillEditor.module.css';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

function QuillEditor(props: any) {
  const [value, setValue] = useState(props.value || '');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { message } = AntdApp.useApp();
  const editorRef = useRef<any>(null);

  // 全屏切换函数
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 自定义工具栏配置，添加全屏按钮
  const modulesDefault = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
      ],
      // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
      // handlers: {
      //   image: async function imageHandler(this: any) {
      //     console.log('this', this);
      //     const input = document.createElement('input');
      //     input.setAttribute('type', 'file');
      //     input.setAttribute('accept', 'image/*');
      //     input.click();

      //     input.onchange = async () => {
      //       const file = input.files?.[0];
      //       if (file) {
      //         const { uploadImgToCloud } = await import('@/lib/cloudinary');

      //         let hideLoading: any;
      //         try {
      //           hideLoading = message.loading('图片上传中...', 0);
      //           const result = await uploadImgToCloud(file);
      //           if (result && result.secure_url) {
      //             const range = this.quill.getSelection();
      //             this.quill.insertEmbed(
      //               range.index,
      //               'image',
      //               result.secure_url
      //             );
      //             this.quill.setSelection(range.index + 1);
      //             hideLoading();

      //             message.success('图片上传成功');
      //           } else {
      //             hideLoading();
      //             message.error('图片上传失败，请重试');
      //           }
      //         } catch (error) {
      //           if (hideLoading) hideLoading();

      //           message.error('图片上传失败，请检查网络连接');
      //         }
      //       }
      //     };
      //   },
      // },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    props.onChange(newValue);
  }

  // 在组件挂载后添加自定义全屏按钮
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const timer = setTimeout(() => {
        const toolbar = document.querySelector('.ql-toolbar');
        if (toolbar) {
          // 移除已存在的全屏按钮
          const existingBtn = toolbar.querySelector('.ql-fullscreen');
          if (existingBtn) {
            existingBtn.remove();
          }

          // 创建新的全屏按钮
          const fullscreenBtn = document.createElement('button');
          fullscreenBtn.className = 'ql-fullscreen';
          fullscreenBtn.type = 'button';
          fullscreenBtn.title = isFullscreen ? '退出全屏' : '全屏编辑';
          fullscreenBtn.innerHTML = isFullscreen
            ? '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/></svg>'
            : '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/></svg>';
          fullscreenBtn.onclick = toggleFullscreen;
          toolbar.appendChild(fullscreenBtn);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isFullscreen, toggleFullscreen]);

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscKey);
      // 防止页面滚动
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = '';
    };
  }, [isFullscreen]);

  return (
    <div
      className={`${styles.editorContainer} ${isFullscreen ? styles.fullscreen : ''}`}
    >
      <ReactQuill
        {...props}
        ref={editorRef}
        value={value}
        onChange={handleChange}
        modules={props.modules || modulesDefault}
        className={styles.editor}
      />
    </div>
  );
}

export default QuillEditor;
